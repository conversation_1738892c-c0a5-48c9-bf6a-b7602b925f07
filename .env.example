# Database
DATABASE_URL=your_database_url_here

# Session
SESSION_SECRET=your_session_secret_here

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id_here
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here

# Stripe (if using subscription features)
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here
STRIPE_API_VERSION=2025-04-30.basil

# Stripe Price IDs for subscription plans
STRIPE_BASIC_PRICE_ID=price_id_for_basic_plan
STRIPE_PROFESSIONAL_PRICE_ID=price_id_for_professional_plan
STRIPE_ENTERPRISE_PRICE_ID=price_id_for_enterprise_plan

# Email (if using email features)
SENDGRID_API_KEY=your_sendgrid_api_key_here
RESEND_API_KEY=your_resend_api_key_here

# Domain-based authentication
# Comma-separated list of emails that can bypass domain restrictions (for testing)
WHITELISTED_EMAILS=<EMAIL>,<EMAIL>

# File Storage
# Replit Object Storage is automatically configured when running on Replit (uses REPL_ID)
# For local development, files are stored in uploads/logos/ directory
# No additional environment variables needed for file storage
