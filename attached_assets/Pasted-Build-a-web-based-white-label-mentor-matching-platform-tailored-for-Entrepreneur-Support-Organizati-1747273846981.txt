Build a web-based, white-label mentor matching platform tailored for Entrepreneur Support Organizations (ESOs). The platform should have the following key functionalities:

Platform Customization & Admin Controls:
<PERSON><PERSON> can fully customize onboarding forms for both mentors and mentees using a no-code, form-builder interface (similar to Typeform).

Ad<PERSON> can define question types (multiple-choice, text input, dropdowns) and mark required fields.

<PERSON><PERSON> can set and update color schemes and branding elements (e.g., logo, primary/secondary colors).

<PERSON><PERSON> should be able to invite prospective mentors and mentees directly via email from the admin dashboard.

AI Matching & Recommendation Flow:
AI should analyze mentee submissions and suggest the top mentor matches based on shared interests, industries, skills, and availability.

AI-generated mentor suggestions should first be sent to a designated admin via email or in-platform notification.

<PERSON><PERSON> can approve, deny, or manually override mentor suggestions.

Communication Workflow:
Upon admin approval, an AI-curated introduction email should automatically be drafted and sent to the mentor and mentee, CCing the admin.

If the mentor has a booking link (provided during onboarding), the system should automatically include this in the introduction email.

After an introduction email is sent, a follow-up email should automatically be triggered to the mentee, asking them to confirm when the mentorship session is scheduled.

Feedback & Rating System:
After each mentorship session, both mentors and mentees receive automated follow-up emails requesting them to rate their experience and provide optional feedback.

Ratings and feedback should be stored in the platform for admin review.

Analytics Dashboard:
<PERSON><PERSON> should be able to view key metrics, including:

Total matches made

Average mentor/mentee ratings

Number of sessions completed

Pending or unconfirmed meetings

Dashboard should visually display activity through charts and summaries.