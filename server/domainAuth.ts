import { storage } from "./storage";

/**
 * Validates if an email domain is allowed for registration/login
 * @param email - The email address to validate
 * @returns Promise<boolean> - true if domain is allowed, false otherwise
 */
export async function isEmailDomainAllowed(email: string): Promise<boolean> {
  try {
    // Extract domain from email
    const domain = email.toLowerCase().split('@')[1];
    if (!domain) {
      return false;
    }

    // Check whitelist from environment first
    const whitelistedEmails = process.env.WHITELISTED_EMAILS?.split(',').map(e => e.trim().toLowerCase()) || [];
    if (whitelistedEmails.includes(email.toLowerCase())) {
      return true;
    }

    // Check if domain exists in orgDomains table
    const orgDomain = await storage.getDomainByName(domain);
    return !!orgDomain;
  } catch (error) {
    console.error("Error validating email domain:", error);
    return false;
  }
}

/**
 * Gets the organization ID for a given email domain
 * @param email - The email address
 * @returns Promise<number | null> - organization ID or null if not found
 */
export async function getOrganizationIdByEmail(email: string): Promise<number | null> {
  try {
    const domain = email.toLowerCase().split('@')[1];
    if (!domain) {
      return null;
    }

    const orgDomain = await storage.getDomainByName(domain);
    return orgDomain?.organizationId || null;
  } catch (error) {
    console.error("Error getting organization by email:", error);
    return null;
  }
}
