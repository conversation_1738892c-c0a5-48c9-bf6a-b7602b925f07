import { Request, Response, NextFunction } from 'express';
import { OAuth2Client } from 'google-auth-library';
import { storage } from './storage';
import { randomUUID } from 'crypto';
import { isEmailDomainAllowed, getOrganizationIdByEmail } from './domainAuth';

// Initialize Google OAuth client
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Verify Google OAuth token and create/update user
export async function verifyGoogleToken(req: Request, res: Response, next: NextFunction) {
  try {
    const { credential } = req.body;
    
    if (!credential) {
      return res.status(400).json({ message: 'Google credential is required' });
    }

    // Verify the Google token
    const ticket = await client.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    if (!payload) {
      return res.status(401).json({ message: 'Invalid Google token' });
    }

    const { sub: googleId, email, given_name: firstName, family_name: lastName, picture: profileImageUrl } = payload;

    if (!email) {
      return res.status(400).json({ message: 'Email is required from Google account' });
    }

    // Validate email domain
    const isDomainAllowed = await isEmailDomainAllowed(email);
    if (!isDomainAllowed) {
      return res.status(403).json({
        message: 'Email domain not authorized. Please contact your administrator.'
      });
    }

    // Check if user already exists by email
    let user = await storage.getUserByEmail(email);
    
    if (user) {
      // Update existing user with Google auth info if needed
      if (user.authType !== 'google' || user.googleId !== googleId) {
        const updatedUser = await storage.updateUser(user.id, {
          authType: 'google',
          googleId,
          firstName: firstName || user.firstName,
          lastName: lastName || user.lastName,
          profileImageUrl: profileImageUrl || user.profileImageUrl,
        });
        user = updatedUser || user;
      }
    } else {
      // Get organization ID from email domain
      const organizationId = await getOrganizationIdByEmail(email);

      // Create new user
      user = await storage.createUser({
        id: randomUUID(),
        email,
        firstName: firstName || null,
        lastName: lastName || null,
        profileImageUrl: profileImageUrl || null,
        role: 'admin', // Default role
        authType: 'google',
        googleId,
        organizationId: organizationId || 1, // Default to org 1 if not found
      });
    }

    // Log the user in using Passport session
    req.login(user, (err) => {
      if (err) {
        console.error('Login error:', err);
        return res.status(500).json({ message: 'Failed to login after Google authentication' });
      }

      // Return the user data (without sensitive fields)
      const { password, googleId: _, ...userWithoutSensitiveData } = user;
      return res.status(200).json(userWithoutSensitiveData);
    });

  } catch (error) {
    console.error('Google OAuth verification error:', error);
    return res.status(500).json({ message: 'Google authentication failed' });
  }
}

// Logout endpoint
export async function logout(req: Request, res: Response) {
  req.logout((err) => {
    if (err) {
      console.error('Logout error:', err);
      return res.status(500).json({ message: 'Logout failed' });
    }
    
    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error('Session destruction error:', err);
        return res.status(500).json({ message: 'Session cleanup failed' });
      }
      
      res.clearCookie('connect.sid'); // Clear the session cookie
      return res.status(200).json({ message: 'Logged out successfully' });
    });
  });
}
