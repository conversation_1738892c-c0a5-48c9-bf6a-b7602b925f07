import { Client } from '@replit/object-storage';
import fs from 'fs/promises';
import path from 'path';
import { nanoid } from 'nanoid';

// File upload configuration
export const UPLOAD_CONFIG = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedMimeTypes: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ],
  localUploadDir: 'uploads/logos'
};

// Initialize Replit Object Storage client
let replitClient: Client | null = null;
try {
  if (process.env.REPL_ID) {
    replitClient = new Client();
  }
} catch (error) {
  console.log('Replit Object Storage not available, using local storage');
}

// Ensure local upload directory exists
async function ensureUploadDir() {
  try {
    await fs.mkdir(UPLOAD_CONFIG.localUploadDir, { recursive: true });
  } catch (error) {
    console.error('Failed to create upload directory:', error);
  }
}

// Generate unique filename with extension
export function generateUniqueFilename(originalName: string): string {
  const ext = path.extname(originalName);
  const timestamp = Date.now();
  const randomId = nanoid(8);
  return `logo_${timestamp}_${randomId}${ext}`;
}

// Validate file type and size
export function validateFile(file: Express.Multer.File): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > UPLOAD_CONFIG.maxFileSize) {
    return { 
      valid: false, 
      error: `File size exceeds 5MB limit. Current size: ${(file.size / 1024 / 1024).toFixed(2)}MB` 
    };
  }

  // Check MIME type
  if (!UPLOAD_CONFIG.allowedMimeTypes.includes(file.mimetype)) {
    return { 
      valid: false, 
      error: `Invalid file type. Allowed types: ${UPLOAD_CONFIG.allowedMimeTypes.join(', ')}` 
    };
  }

  return { valid: true };
}

// Upload file to appropriate storage
export async function uploadLogo(file: Express.Multer.File): Promise<{ filename: string; url: string }> {
  const validation = validateFile(file);
  if (!validation.valid) {
    throw new Error(validation.error);
  }

  const filename = generateUniqueFilename(file.originalname);

  if (replitClient) {
    // Upload to Replit Object Storage
    try {
      const result = await replitClient.uploadFromBytes(filename, file.buffer);

      if (!result.ok) {
        console.error('Failed to upload to Replit Object Storage:', result.error);
        throw new Error('Failed to upload file to storage');
      }

      return {
        filename,
        url: `/api/logo/${filename}` // We'll serve through our API
      };
    } catch (error) {
      console.error('Failed to upload to Replit Object Storage:', error);
      throw new Error('Failed to upload file to storage');
    }
  } else {
    // Upload to local storage
    try {
      await ensureUploadDir();
      const filePath = path.join(UPLOAD_CONFIG.localUploadDir, filename);
      await fs.writeFile(filePath, file.buffer);
      
      return {
        filename,
        url: `/api/logo/${filename}`
      };
    } catch (error) {
      console.error('Failed to upload to local storage:', error);
      throw new Error('Failed to upload file to local storage');
    }
  }
}

// Download file from appropriate storage
export async function downloadLogo(filename: string): Promise<{ buffer: Buffer; contentType: string } | null> {
  if (replitClient) {
    // Download from Replit Object Storage
    try {
      const result = await replitClient.downloadAsBytes(filename);

      if (!result.ok) {
        console.error('Failed to download from Replit Object Storage:', result.error);
        return null;
      }

      // Determine content type from filename extension
      const ext = path.extname(filename).toLowerCase();
      let contentType = 'image/jpeg'; // default

      switch (ext) {
        case '.png': contentType = 'image/png'; break;
        case '.gif': contentType = 'image/gif'; break;
        case '.webp': contentType = 'image/webp'; break;
        case '.svg': contentType = 'image/svg+xml'; break;
        case '.jpg':
        case '.jpeg': contentType = 'image/jpeg'; break;
      }

      return {
        buffer: result.value[0], // downloadAsBytes returns Result<[Buffer], RequestError>
        contentType
      };
    } catch (error) {
      console.error('Failed to download from Replit Object Storage:', error);
      return null;
    }
  } else {
    // Download from local storage
    try {
      const filePath = path.join(UPLOAD_CONFIG.localUploadDir, filename);
      const buffer = await fs.readFile(filePath);

      // Determine content type from filename extension
      const ext = path.extname(filename).toLowerCase();
      let contentType = 'image/jpeg'; // default

      switch (ext) {
        case '.png': contentType = 'image/png'; break;
        case '.gif': contentType = 'image/gif'; break;
        case '.webp': contentType = 'image/webp'; break;
        case '.svg': contentType = 'image/svg+xml'; break;
        case '.jpg':
        case '.jpeg': contentType = 'image/jpeg'; break;
      }

      return {
        buffer,
        contentType
      };
    } catch (error) {
      console.error('Failed to download from local storage:', error);
      return null;
    }
  }
}

// Delete file from appropriate storage
export async function deleteLogo(filename: string): Promise<boolean> {
  if (replitClient) {
    // Delete from Replit Object Storage
    try {
      const result = await replitClient.delete(filename);

      if (!result.ok) {
        console.error('Failed to delete from Replit Object Storage:', result.error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to delete from Replit Object Storage:', error);
      return false;
    }
  } else {
    // Delete from local storage
    try {
      const filePath = path.join(UPLOAD_CONFIG.localUploadDir, filename);
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      console.error('Failed to delete from local storage:', error);
      return false;
    }
  }
}
