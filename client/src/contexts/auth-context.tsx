import { createContext, useContext, ReactNode } from "react";
import { useQuery, useMutation, UseMutationResult } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { apiRequest, queryClient } from "@/lib/queryClient";

// User type now matching what comes from the API
interface User {
  id: string;
  email: string;
  username?: string | null;
  role: string;
  firstName: string | null;
  lastName: string | null;
  profileImageUrl: string | null;
  organizationId: number | null;
  authType?: string | null;
}

interface GoogleLoginData {
  credential: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  isLoading: boolean;
  googleLoginMutation: UseMutationResult<User, Error, GoogleLoginData>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Fetch user data from the API
  const { data: userData, isLoading } = useQuery<User | null>({
    queryKey: ["/api/auth/user"],
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    queryFn: async ({ queryKey }) => {
      try {
        const res = await fetch(queryKey[0] as string, {
          credentials: "include",
        });

        if (res.status === 401) {
          return null;
        }

        if (!res.ok) {
          throw new Error(`Failed to fetch user: ${res.status}`);
        }

        return await res.json();
      } catch (error) {
        console.error("Error fetching user:", error);
        return null;
      }
    }
  });

  // Ensure we always have a defined user state (either the user data or null)
  const user = userData || null;

  // Google OAuth login mutation
  const googleLoginMutation = useMutation<User, Error, GoogleLoginData>({
    mutationFn: async (credentials) => {
      const res = await apiRequest("POST", "/api/auth/google", credentials);
      console.log("googleLoginMutation res:", res);
      if (!res.ok) {
        const errorData = await res.json();
        console.log(errorData)
        
        throw new Error(errorData.message || "Google login failed")
      }
      return await res.json();
    },
    onSuccess: (userData) => {
      queryClient.setQueryData(["/api/auth/user"], userData);
      toast({
        title: "Login successful",
        description: "You have been logged in successfully."
      });
      setLocation("/");
    },
    onError: (error) => {
      toast({
        title: "Login failed",
        description: error.message || "An error occurred during login",
        variant: "destructive"
      });
    }
  });

  // Logout function
  const logout = async () => {
    try {
      await apiRequest("POST", "/api/logout", {});
      queryClient.setQueryData(["/api/auth/user"], null);
      toast({
        title: "Logged out",
        description: "You have been logged out successfully."
      });
      setLocation("/auth/login");
    } catch (error) {
      // Even if logout fails on server, clear local state
      queryClient.setQueryData(["/api/auth/user"], null);
      setLocation("/auth/login");
    }
  };

  const updateUser = (userData: Partial<User>) => {
    // This will be handled by the query invalidation
    // Kept for compatibility with existing code
    toast({
      title: "Profile updated",
      description: "Your profile information has been updated successfully."
    });
  };

  return (
    <AuthContext.Provider value={{
      user: user || null,
      isAuthenticated: !!user,
      logout,
      updateUser,
      isLoading,
      googleLoginMutation
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}