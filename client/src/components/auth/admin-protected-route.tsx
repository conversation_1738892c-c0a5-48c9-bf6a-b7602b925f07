import { useAuth } from "@/contexts/auth-context";
import { Loader2, Shield, AlertTriangle } from "lucide-react";
import { Route } from "wouter";

interface AdminProtectedRouteProps {
  path: string;
  component: React.ComponentType<any>;
}

export function AdminProtectedRoute({ path, component: Component }: AdminProtectedRouteProps) {
  const { user, isLoading } = useAuth();

  return (
    <Route path={path}>
      {(params) => {
        if (isLoading) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <Loader2 className="h-8 w-8 animate-spin text-border" />
            </div>
          );
        }

        if (!user) {
          window.location.href = "/auth/login";
          return null;
        }

        if (user.role !== 'mmh-admin') {
          return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
              <div className="text-center p-8 bg-white rounded-lg shadow-md max-w-md">
                <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
                <p className="text-gray-500 mb-4">MMH Administrator privileges required to access this page.</p>
                <div className="flex items-center justify-center text-sm text-gray-400">
                  <Shield className="h-4 w-4 mr-1" />
                  MMH Admin Only
                </div>
              </div>
            </div>
          );
        }

        return <Component {...params} />;
      }}
    </Route>
  );
}
