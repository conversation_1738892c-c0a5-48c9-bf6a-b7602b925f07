import { useAuth } from "@/contexts/auth-context";
import { Loader2 } from "lucide-react";
import { Redirect } from "wouter";

export function RoleBasedRedirect() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-border" />
      </div>
    );
  }

  if (!user) {
    window.location.href = "/auth/login";
    return null;
  }

  // Redirect MMH admins to their dedicated dashboard
  if (user.role === 'mmh-admin') {
    return <Redirect to="/mmh-admin/dashboard" />;
  }

  // Redirect regular users to the main dashboard
  return <Redirect to="/dashboard" />;
}
