import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Helmet } from "react-helmet";
import { useAuth } from "@/contexts/auth-context";
import { Loader2 } from "lucide-react";
import { useLocation } from "wouter";
import { GoogleLogin } from "@react-oauth/google";

export default function Login() {
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const { user, googleLoginMutation } = useAuth();

  // Redirect if already logged in
  if (user) {
    setLocation("/");
    return null;
  }

  const handleGoogleSuccess = async (credentialResponse: any) => {
    try {
      await googleLoginMutation.mutateAsync({ credential: credentialResponse.credential });
    } catch (error) {
      console.error("Google login error:", error);
    }
  };

  const handleGoogleError = () => {
    toast({
      title: "Login failed",
      description: "Google authentication failed. Please try again.",
      variant: "destructive"
    });
  };

  return (
    <>
      <Helmet>
        <title>Login | Mentor Match Platform</title>
        <meta name="description" content="Login to access your mentor matching platform" />
      </Helmet>
      
      <div className="flex min-h-screen bg-gray-50">
        {/* Login Form Side */}
        <div className="flex w-full md:w-1/2 items-center justify-center p-6">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold">Mentor Match Platform</CardTitle>
              <CardDescription>
                Log in to manage your mentor matching program
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-6">
                  Sign in with your Google account to access the platform
                </p>

                {googleLoginMutation.isPending ? (
                  <Button disabled className="w-full">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </Button>
                ) : (
                  <GoogleLogin
                    onSuccess={handleGoogleSuccess}
                    onError={handleGoogleError}
                    useOneTap={false}
                    theme="outline"
                    size="large"
                    width="100%"
                  />
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Hero Side */}
        <div className="hidden md:flex md:w-1/2 bg-blue-600 text-white flex-col justify-center items-center p-12">
          <div className="max-w-md space-y-6">
            <h1 className="text-4xl font-bold">Mentor Match Platform</h1>
            <p className="text-xl">
              Connect mentors and mentees efficiently with our intelligent matching algorithm.
            </p>
            <ul className="space-y-3">
              <li className="flex items-center">
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Smart matching based on skills and goals
              </li>
              <li className="flex items-center">
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Streamlined onboarding process
              </li>
              <li className="flex items-center">
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Track and measure mentoring outcomes
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
}