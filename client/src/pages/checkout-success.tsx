import * as React from "react";
import { Helmet } from "react-helmet";
import { Link, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { CheckCircle, Loader } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

export default function CheckoutSuccess() {
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const [verifying, setVerifying] = React.useState(true);

  React.useEffect(() => {
    const verifySession = async () => {
      try {
        // Get session_id from URL
        const params = new URLSearchParams(window.location.search);
        const sessionId = params.get('session_id');

        if (!sessionId) {
          throw new Error('No session ID found');
        }

        // Verify the session and save subscription details
        const response = await apiRequest(
          'GET',
          `/api/verify-checkout-session?session_id=${sessionId}`
        );

        if (!response.ok) {
          throw new Error('Failed to verify session');
        }

        setVerifying(false);
      } catch (error) {
        console.error('Error verifying session:', error);
        toast({
          title: 'Error',
          description: 'Failed to verify subscription. Please contact support.',
          variant: 'destructive'
        });
        navigate('/subscription-plans');
      }
    };

    verifySession();
  }, []);

  if (verifying) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh]">
        <Loader className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-gray-600">Verifying your subscription...</p>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Subscription Successful | Mentor Match</title>
        <meta name="description" content="Your subscription has been activated successfully" />
      </Helmet>

      <div className="flex flex-col items-center justify-center min-h-[70vh]">
        <Card className="w-full max-w-md">
          <CardHeader className="pb-2">
            <div className="flex justify-center mb-4">
              <CheckCircle className="h-16 w-16 text-primary" />
            </div>
            <CardTitle className="text-center text-2xl">Subscription Activated!</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-gray-600 mb-4">
              Thank you for subscribing to Mentor Match. Your payment has been processed successfully!
            </p>
            <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-4">
              <p className="text-sm text-blue-800 font-medium mb-2">
                🔄 Your subscription is being activated
              </p>
              <p className="text-sm text-blue-700">
                We're setting up your account with the new subscription features.
                This usually takes just a few moments. You can refresh this page or
                check your subscription status in a few minutes.
              </p>
            </div>
            <div className="bg-gray-50 border border-gray-100 rounded-lg p-4">
              <p className="text-sm text-gray-600">
                <strong>What's next?</strong><br />
                • Your subscription will be automatically activated<br />
                • You'll receive access to all plan features<br />
                • No further action is required from you
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center gap-3">
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
            >
              Refresh Status
            </Button>
            <Link href="/subscription-plans">
              <Button>View Subscription</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </>
  );
}