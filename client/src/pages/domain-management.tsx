import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Helmet } from "react-helmet";
import { Globe, Plus, Trash2, Edit2, Building } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface Organization {
  id: number;
  name: string;
}

interface OrgDomain {
  id: number;
  organizationId: number;
  domain: string;
  createdAt: string;
  updatedAt: string;
}

export default function DomainManagement() {
  const [selectedOrgId, setSelectedOrgId] = useState<string>("");
  const [newDomain, setNewDomain] = useState("");
  const [editingDomain, setEditingDomain] = useState<{id: number, domain: string} | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: organizations } = useQuery<Organization[]>({
    queryKey: ['/api/organizations'],
  });

  const { data: domains, isLoading: isLoadingDomains } = useQuery<OrgDomain[]>({
    queryKey: [`/api/organizations/${selectedOrgId}/domains`],
    enabled: !!selectedOrgId,
  });

  const createDomainMutation = useMutation({
    mutationFn: async (domain: string) => {
      const res = await apiRequest("POST", `/api/organizations/${selectedOrgId}/domains`, { domain });
      if (!res.ok) throw new Error("Failed to create domain");
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/organizations/${selectedOrgId}/domains`] });
      setNewDomain("");
      toast({ title: "Domain added successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });

  const updateDomainMutation = useMutation({
    mutationFn: async ({ domainId, domain }: { domainId: number, domain: string }) => {
      const res = await apiRequest("PUT", `/api/organizations/${selectedOrgId}/domains/${domainId}`, { domain });
      if (!res.ok) throw new Error("Failed to update domain");
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/organizations/${selectedOrgId}/domains`] });
      setEditingDomain(null);
      toast({ title: "Domain updated successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });

  const deleteDomainMutation = useMutation({
    mutationFn: async (domainId: number) => {
      const res = await apiRequest("DELETE", `/api/organizations/${selectedOrgId}/domains/${domainId}`);
      if (!res.ok) throw new Error("Failed to delete domain");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/organizations/${selectedOrgId}/domains`] });
      toast({ title: "Domain deleted successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });

  return (
    <>
      <Helmet>
        <title>Domain Management | MMH Admin</title>
        <meta name="description" content="Manage organization domains for authentication" />
      </Helmet>

      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-semibold text-gray-800 flex items-center">
            <Globe className="mr-3 h-6 w-6 text-primary" />
            Domain Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">Configure email domains for organization access control</p>
        </div>
      </div>

      {/* Organization Selector */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="mr-2 h-5 w-5" />
            Select Organization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={selectedOrgId} onValueChange={setSelectedOrgId}>
            <SelectTrigger className="w-full max-w-md">
              <SelectValue placeholder="Choose an organization to manage domains" />
            </SelectTrigger>
            <SelectContent>
              {organizations?.map((org) => (
                <SelectItem key={org.id} value={org.id.toString()}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedOrgId && (
        <Card>
          <CardHeader>
            <CardTitle>Authorized Domains</CardTitle>
            <p className="text-sm text-gray-500">
              Users with email addresses from these domains can access the application
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Add Domain */}
            <div className="flex gap-2">
              <Input
                placeholder="Enter domain (e.g., company.com)"
                value={newDomain}
                onChange={(e) => setNewDomain(e.target.value)}
                className="flex-1"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && newDomain.trim()) {
                    createDomainMutation.mutate(newDomain);
                  }
                }}
              />
              <Button 
                onClick={() => createDomainMutation.mutate(newDomain)}
                disabled={!newDomain.trim() || createDomainMutation.isPending}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Domain
              </Button>
            </div>

            {/* Domains List */}
            <div className="space-y-2">
              {isLoadingDomains ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                  Loading domains...
                </div>
              ) : domains?.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Globe className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>No domains configured</p>
                  <p className="text-sm">Add a domain above to get started</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {domains?.map((domain) => (
                    <div key={domain.id} className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
                      {editingDomain?.id === domain.id ? (
                        <div className="flex gap-2 flex-1">
                          <Input
                            value={editingDomain.domain}
                            onChange={(e) => setEditingDomain(editingDomain ? {...editingDomain, domain: e.target.value} : null)}
                            className="flex-1"
                            onKeyPress={(e) => {
                              if (e.key === 'Enter' && editingDomain) {
                                updateDomainMutation.mutate({
                                  domainId: domain.id, 
                                  domain: editingDomain.domain
                                });
                              }
                            }}
                          />
                          <Button 
                            size="sm"
                            onClick={() => editingDomain && updateDomainMutation.mutate({
                              domainId: domain.id, 
                              domain: editingDomain.domain
                            })}
                            disabled={updateDomainMutation.isPending}
                          >
                            Save
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => setEditingDomain(null)}
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <>
                          <div className="flex items-center">
                            <Globe className="h-4 w-4 mr-2 text-gray-400" />
                            <span className="font-mono text-sm font-medium">{domain.domain}</span>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingDomain({id: domain.id, domain: domain.domain})}
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => deleteDomainMutation.mutate(domain.id)}
                              disabled={deleteDomainMutation.isPending}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Help Text */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">How Domain Authentication Works</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Users can only sign up/login if their email domain is listed here</li>
                <li>• Domains are case-insensitive (company.com = Company.com)</li>
                <li>• Users are automatically assigned to this organization</li>
                <li>• Whitelisted emails in .env can bypass domain restrictions</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}
