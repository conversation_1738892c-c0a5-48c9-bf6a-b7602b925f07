import { createRoot } from "react-dom/client";
import { QueryClientProvider } from "@tanstack/react-query";
import { GoogleOAuthProvider } from "@react-oauth/google";
import App from "./App";
import { BrandingProvider } from "@/contexts/branding-context";
import { AuthProvider } from "@/contexts/auth-context";
import { queryClient } from "@/lib/queryClient";
import "./index.css";

// Google OAuth Client ID - this should be set in environment variables
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || "your-google-client-id";

console.log("GOOGLE_CLIENT_ID:", GOOGLE_CLIENT_ID);
console.log("Environment variables:", import.meta.env);

// Only render GoogleOAuthProvider if we have a valid client ID
const renderApp = () => {
  if (!GOOGLE_CLIENT_ID || GOOGLE_CLIENT_ID === "your-google-client-id") {
    console.error("VITE_GOOGLE_CLIENT_ID is not properly configured");
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        <h1>Configuration Error</h1>
        <p>Google OAuth Client ID is not configured. Please set VITE_GOOGLE_CLIENT_ID in your environment variables.</p>
      </div>
    );
  }

  return (
    <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <BrandingProvider>
            <App />
          </BrandingProvider>
        </AuthProvider>
      </QueryClientProvider>
    </GoogleOAuthProvider>
  );
};

createRoot(document.getElementById("root")!).render(renderApp());
